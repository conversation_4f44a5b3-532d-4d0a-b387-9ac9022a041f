import React from 'react';
import { Composition } from 'remotion';
import TextTypeWithSlide from '../components/TextTypeWithSlide';

export const TextTypeSlideDemo: React.FC = () => {
  return (
    <TextTypeWithSlide
      text="但睡眠的核心不是早，而是规律"
      typingSpeed={100}
      slideStartFrame={240} // 4秒后开始滑动
      slideDuration={90} // 滑动持续1.5秒
      className="max-w-4xl"
    />
  );
};

// Composition 配置
export const textTypeSlideComposition: Composition = {
  id: 'TextTypeSlideDemo',
  component: TextTypeSlideDemo,
  durationInFrames: 420, // 7秒总时长
  fps: 60,
  width: 1920,
  height: 1080,
  props: {},
};

export default TextTypeSlideDemo;
